# LMS - Learning Management System

## Setup Instructions

### Prerequisites
- <PERSON><PERSON> and <PERSON>er Compose
- Git

### Installation

1. **Clone the project**
   ```bash
   git clone <repository-url>
   cd LMS
   ```

2. **Setup environment file**
   ```bash
   cp .env.example .env
   ```

3. **Build and start Docker containers**
   ```bash
   docker-compose --env-file .env up -d --build
   ```

4. **Access the application container**
   ```bash
   docker-compose exec app sh
   ```

5. **Install PHP dependencies**

   **For Production:**
   ```bash
   composer install --no-dev --no-interaction --optimize-autoloader --no-scripts --no-cache
   ```

   **For Development:**
   ```bash
   composer install --no-interaction --optimize-autoloader --no-scripts --no-cache
   ```

6. **Generate application key**
   ```bash
   php artisan key:generate
   ```

7. **Install and build frontend assets**
   ```bash
   npm install
   npm run build
   ```

### Configuration

After installation, make sure to:
- Configure your database settings in the `.env` file
- Set up any additional environment variables as needed
- Run database migrations if applicable: `php artisan migrate`
