services:
    app:
        build:
            context: .
            dockerfile: docker/php/Dockerfile
            target: ${APP_ENV:-development}
        image: lunch-management-app
        container_name: lunch-management-app
        restart: unless-stopped
        volumes:
            - ./:/var/www
        user: "1000:1000"
        working_dir: /var/www
        environment:
            REDIS_HOST: redis
        depends_on:
            - redis
        networks:
            - lunch-management

    web:
        image: nginx:alpine
        container_name: lunch-management-web
        restart: unless-stopped
        ports:
            - "${APP_PORT:-8000}:80"
        volumes:
            - ./:/var/www
            - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
        depends_on:
            - app
        networks:
            - lunch-management

    redis:
        image: redis:alpine
        container_name: lunch-management-redis
        restart: unless-stopped
        ports:
            - "6379:6379"
        networks:
            - lunch-management

networks:
    lunch-management:
        driver: bridge
