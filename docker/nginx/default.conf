server {
    listen          80;

    error_log       /var/log/nginx/error.log;
    access_log      /var/log/nginx/access.log;

    root            /var/www/public;
    index           index.php;

    client_max_body_size 1000M;

    location /storage/ {
        alias /var/www/public/storage/;
        try_files $uri $uri/ =404;
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param HTTPS $http_x_forwarded_proto;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }

    location ~ /\.ht {
        deny all;
    }

    # Extra Security Headers (optional but recommended)
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
}
