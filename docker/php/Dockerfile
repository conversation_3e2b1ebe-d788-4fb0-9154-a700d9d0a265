# ---------------------------
# Stage 1: Base setup
# ---------------------------
FROM php:8.2-fpm-alpine AS base

# Arguments for user and group
ARG user=lms
ARG uid=1000

# Set working directory
WORKDIR /var/www

# Install system dependencies and PHP extensions
RUN apk add --no-cache \
    git \
    curl \
    zip \
    unzip \
    bash \
    libzip-dev \
    libpng-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    oniguruma-dev \
    icu-dev \
    libxml2-dev \
    sqlite-dev \
    pkgconfig \
    nodejs \
    npm && \
    docker-php-ext-configure gd --with-freetype --with-jpeg && \
    docker-php-ext-install \
        pdo \
        pdo_mysql \
        mbstring \
        exif \
        pcntl \
        bcmath \
        gd \
        zip \
        intl \
        calendar

# Add non-root user
RUN adduser -D -u $uid $user && \
    mkdir -p /home/<USER>
    chown -R $user:www-data /home/<USER>

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# ---------------------------
# Stage 2: Development
# ---------------------------
FROM base AS development

# Set working directory
WORKDIR /var/www

# Copy composer files and assign ownership
COPY --chown=$user:www-data composer.json composer.lock ./

# Copy full application code and assign ownership
COPY --chown=$user:www-data . .

# Set correct permissions for Laravel directories
RUN chmod -R 775 storage bootstrap/cache && \
    chown -R $user:www-data storage bootstrap/cache && \
    chown -R $user:www-data /var/www

# Switch to non-root user
USER $user

# Expose port for PHP-FPM
EXPOSE 9000

# Start PHP-FPM
CMD ["php-fpm"]

# ---------------------------
# Stage 3: Production
# ---------------------------
FROM base AS production

# Set working directory
WORKDIR /var/www

# Copy composer files
COPY --chown=$user:www-data composer.json composer.lock ./

# Copy application code
COPY --chown=$user:www-data . .

# Set correct permissions for Laravel directories
RUN chmod -R 775 storage bootstrap/cache && \
    chown -R $user:www-data storage bootstrap/cache && \
    chown -R $user:www-data /var/www

# Switch to non-root user
USER $user

# Expose PHP-FPM port
EXPOSE 9000

# Start PHP-FPM
CMD ["php-fpm"]
