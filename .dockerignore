# Dependency directories (excluded from image, mounted at runtime)
vendor/
node_modules/

# <PERSON><PERSON> logs and sensitive storage files
storage/*.log
storage/*.key
storage/framework/cache/**
storage/framework/sessions/**
storage/framework/views/**

# IDE and system files
.idea/
.vscode/
*.swp
*.swo
*.DS_Store
Thumbs.db

# Git
.git/
.gitattributes

# Docker and build-related
.dockerignore
docker/php/Dockerfile

# Testing files
phpunit.xml
phpunit.xml.dist
tests/

# Miscellaneous
*.log
*.cache
*.bak
*.tmp
